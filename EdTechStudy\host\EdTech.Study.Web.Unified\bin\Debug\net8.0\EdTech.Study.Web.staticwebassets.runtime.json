{"ContentRoots": ["C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.components.web\\8.1.4\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.1\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.snackbar\\1.4.1\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.datagrid\\1.4.1\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.bla<PERSON><PERSON>\\8.1.4\\staticwebassets\\", "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\"], "Root": {"Children": {"_content": {"Children": {"Volo.Abp.AspNetCore.Components.Web": {"Children": {"libs": {"Children": {"abp": {"Children": {"js": {"Children": {"abp.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "libs/abp/js/abp.js"}, "Patterns": null}, "lang-utils.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "libs/abp/js/lang-utils.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Blazorise": {"Children": {"blazorise.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazorise.css"}, "Patterns": null}, "blazorise.min.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazorise.min.css"}, "Patterns": null}, "breakpoint.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "breakpoint.js"}, "Patterns": null}, "button.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "button.js"}, "Patterns": null}, "closable.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "closable.js"}, "Patterns": null}, "colorPicker.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "colorPicker.js"}, "Patterns": null}, "datePicker.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "datePicker.js"}, "Patterns": null}, "dragDrop.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "dragDrop.js"}, "Patterns": null}, "dropdown.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "dropdown.js"}, "Patterns": null}, "fileEdit.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "fileEdit.js"}, "Patterns": null}, "filePicker.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "filePicker.js"}, "Patterns": null}, "floatingUi.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "floatingUi.js"}, "Patterns": null}, "inputMask.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "inputMask.js"}, "Patterns": null}, "io.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "io.js"}, "Patterns": null}, "memoEdit.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "memoEdit.js"}, "Patterns": null}, "numericPicker.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "numericPicker.js"}, "Patterns": null}, "observer.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "observer.js"}, "Patterns": null}, "table.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "table.js"}, "Patterns": null}, "textEdit.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "textEdit.js"}, "Patterns": null}, "theme.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "theme.js"}, "Patterns": null}, "timePicker.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "timePicker.js"}, "Patterns": null}, "tooltip.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "tooltip.js"}, "Patterns": null}, "utilities.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "utilities.js"}, "Patterns": null}, "validators": {"Children": {"DateTimeMaskValidator.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "validators/DateTimeMaskValidator.js"}, "Patterns": null}, "NoValidator.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "validators/NoValidator.js"}, "Patterns": null}, "NumericMaskValidator.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "validators/NumericMaskValidator.js"}, "Patterns": null}, "RegExMaskValidator.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "validators/RegExMaskValidator.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "vendors": {"Children": {"autoNumeric.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "vendors/autoNumeric.js"}, "Patterns": null}, "Behave.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "vendors/Behave.js"}, "Patterns": null}, "flatpickr.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "vendors/flatpickr.js"}, "Patterns": null}, "floating-ui-core.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "vendors/floating-ui-core.js"}, "Patterns": null}, "floating-ui.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "vendors/floating-ui.js"}, "Patterns": null}, "inputmask.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "vendors/inputmask.js"}, "Patterns": null}, "jsencrypt.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "vendors/jsencrypt.js"}, "Patterns": null}, "Pickr.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "vendors/Pickr.js"}, "Patterns": null}, "sha512.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "vendors/sha512.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Blazorise.Snackbar": {"Children": {"blazorise.snackbar.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "blazorise.snackbar.css"}, "Patterns": null}, "blazorise.snackbar.min.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "blazorise.snackbar.min.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Blazorise.DataGrid": {"Children": {"datagrid.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "datagrid.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Volo.Abp.BlazoriseUI": {"Children": {"volo.abp.blazoriseui.css": {"Children": null, "Asset": {"ContentRootIndex": 4, "SubPath": "volo.abp.blazoriseui.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "client-proxies": {"Children": {"edtect-study-proxy.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "client-proxies/edtect-study-proxy.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "css": {"Children": {"lesson-config-custom.css": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "css/lesson-config-custom.css"}, "Patterns": null}, "lesson-config.css": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "css/lesson-config.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "EdTech": {"Children": {"reactapp": {"Children": {"175.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/175.js"}, "Patterns": null}, "175.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/175.js.map"}, "Patterns": null}, "185.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/185.js"}, "Patterns": null}, "185.js.LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/185.js.LICENSE.txt"}, "Patterns": null}, "185.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/185.js.map"}, "Patterns": null}, "525.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/525.js"}, "Patterns": null}, "525.js.LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/525.js.LICENSE.txt"}, "Patterns": null}, "525.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/525.js.map"}, "Patterns": null}, "652.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/652.js"}, "Patterns": null}, "652.js.LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/652.js.LICENSE.txt"}, "Patterns": null}, "652.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/652.js.map"}, "Patterns": null}, "915.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/915.js"}, "Patterns": null}, "915.js.LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/915.js.LICENSE.txt"}, "Patterns": null}, "915.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/915.js.map"}, "Patterns": null}, "assets": {"Children": {"css": {"Children": {"app.css": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/css/app.css"}, "Patterns": null}, "app.css.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/css/app.css.map"}, "Patterns": null}, "vendor.css": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/css/vendor.css"}, "Patterns": null}, "vendor.css.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/css/vendor.css.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "images": {"Children": {"CoordinateFinderGame.jpg": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/CoordinateFinderGame.jpg"}, "Patterns": null}, "Demo.png": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/Demo.png"}, "Patterns": null}, "Game.jpg": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/Game.jpg"}, "Patterns": null}, "layers-2x.png": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/layers-2x.png"}, "Patterns": null}, "layers.png": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/layers.png"}, "Patterns": null}, "Layout.png": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/Layout.png"}, "Patterns": null}, "marker-icon.png": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/marker-icon.png"}, "Patterns": null}, "marker-shadow.png": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/marker-shadow.png"}, "Patterns": null}, "MillionaireGame.jpg": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/MillionaireGame.jpg"}, "Patterns": null}, "practiceBackground.png": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/practiceBackground.png"}, "Patterns": null}, "Quiz.jpg": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/Quiz.jpg"}, "Patterns": null}, "QuizCardGame.jpg": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/QuizCardGame.jpg"}, "Patterns": null}, "Simulator.jpg": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/Simulator.jpg"}, "Patterns": null}, "TreasureHuntGame.jpg": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/assets/images/TreasureHuntGame.jpg"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "BasePage.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/BasePage.js"}, "Patterns": null}, "BasePage.js.LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/BasePage.js.LICENSE.txt"}, "Patterns": null}, "BasePage.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/BasePage.js.map"}, "Patterns": null}, "demo.html": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/demo.html"}, "Patterns": null}, "DemoLessonPage.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/DemoLessonPage.js"}, "Patterns": null}, "DemoLessonPage.js.LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/DemoLessonPage.js.LICENSE.txt"}, "Patterns": null}, "DemoLessonPage.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/DemoLessonPage.js.map"}, "Patterns": null}, "exam-management-router.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/exam-management-router.js"}, "Patterns": null}, "exam-management-router.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/exam-management-router.js.map"}, "Patterns": null}, "ExamManagementPage.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/ExamManagementPage.js"}, "Patterns": null}, "ExamManagementPage.js.LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/ExamManagementPage.js.LICENSE.txt"}, "Patterns": null}, "ExamManagementPage.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/ExamManagementPage.js.map"}, "Patterns": null}, "exams.html": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/exams.html"}, "Patterns": null}, "iconStore.html": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/iconStore.html"}, "Patterns": null}, "IconStorePage.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/IconStorePage.js"}, "Patterns": null}, "IconStorePage.js.LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/IconStorePage.js.LICENSE.txt"}, "Patterns": null}, "IconStorePage.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/IconStorePage.js.map"}, "Patterns": null}, "index.html": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/index.html"}, "Patterns": null}, "PracticeExamPage.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/PracticeExamPage.js"}, "Patterns": null}, "PracticeExamPage.js.LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/PracticeExamPage.js.LICENSE.txt"}, "Patterns": null}, "PracticeExamPage.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/PracticeExamPage.js.map"}, "Patterns": null}, "practiceExams.html": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/practiceExams.html"}, "Patterns": null}, "preview.html": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/preview.html"}, "Patterns": null}, "PreviewLessonPage.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/PreviewLessonPage.js"}, "Patterns": null}, "PreviewLessonPage.js.LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/PreviewLessonPage.js.LICENSE.txt"}, "Patterns": null}, "PreviewLessonPage.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/PreviewLessonPage.js.map"}, "Patterns": null}, "question.html": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/question.html"}, "Patterns": null}, "QuestionPage.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/QuestionPage.js"}, "Patterns": null}, "QuestionPage.js.LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/QuestionPage.js.LICENSE.txt"}, "Patterns": null}, "QuestionPage.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/QuestionPage.js.map"}, "Patterns": null}, "runtime.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/runtime.js"}, "Patterns": null}, "runtime.js.map": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "EdTech/reactapp/runtime.js.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "images": {"Children": {"home": {"Children": {"logo.png": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "images/home/<USER>"}, "Patterns": null}, "profile-avatar.png": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "images/home/<USER>"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "js": {"Children": {"common": {"Children": {"lesson-init-module.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "js/common/lesson-init-module.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 5, "Pattern": "**", "Depth": 0}]}}